
import type { Middleware } from "../types";
import { ResponseUtils } from "../utils";

export function createApiKeyAuthMiddleware(
): Middleware {

  return async (context, next) => {
    try {
      const { req } = context;
      const apiKey = req.headers.get('x-api-key')
      if (!apiKey || apiKey !== '842797524') {
        throw ResponseUtils.unauthorized("Missing authentication");
      }
      await next();
    } catch (error) {
      if (error instanceof Response) throw error;
      throw ResponseUtils.internalError("Authentication error");
    }
  };
}
