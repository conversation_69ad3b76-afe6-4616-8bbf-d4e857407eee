import { Hono } from 'hono';
import { HonoApiRouteBuilder } from '../builders/hono-route-builder';

interface RouteModule {
  default?: Hono;
  routes?: Hono;
  builder?: HonoApiRouteBuilder;
  register?: (app: Hono) => void;
  config?: {
    enabled?: boolean;
  };
}

interface AutoRouterOptions {
  routesDir: string;
  excludePatterns?: string[];
}

/**
 * 简化的自动路由注册器
 * 只支持最常用的模式，减少复杂性
 */
export class AutoRouter {
  private app: Hono;
  private options: AutoRouterOptions;
  private registeredRoutes = new Map<string, RouteModule>();

  constructor(app: Hono, options: AutoRouterOptions) {
    this.app = app;
    this.options = {
      excludePatterns: ['**/*.test.*', '**/*.spec.*', '**/index.*'],
      ...options
    };
  }

  /**
   * 自动发现并注册所有路由模块
   */
  async registerRoutes(): Promise<void> {
    try {
      const routeFiles = await this.discoverRouteFiles();

      for (const filePath of routeFiles) {
        await this.registerRouteFile(filePath);
      }

    } catch (error) {
      console.error('❌ 路由注册失败:', error);
      throw error;
    }
  }

  /**
   * 发现路由文件（使用 glob 库，Windows 兼容）
   */
  private async discoverRouteFiles(): Promise<string[]> {
    const { glob } = await import('glob');
    const path = await import('path');

    // 规范化路径，确保在 Windows 上使用正确的分隔符
    const normalizedRoutesDir = path.resolve(this.options.routesDir);

    // 使用 POSIX 风格的路径分隔符进行 glob 匹配（glob 库要求）
    const posixRoutesDir = normalizedRoutesDir.replace(/\\/g, '/');
    const pattern = `${posixRoutesDir}/**/*.{ts,js}`;



    const files = await glob(pattern, {
      ignore: this.options.excludePatterns?.map(p => `${posixRoutesDir}/${p}`) || [],
      windowsPathsNoEscape: true // Windows 路径支持
    });


    return files.filter(file => {
      // 使用 path.relative 来正确处理相对路径
      const relativePath = path.relative(normalizedRoutesDir, file);
      const normalizedRelativePath = relativePath.replace(/\\/g, '/'); // 统一使用 / 分隔符

      return !normalizedRelativePath.startsWith('_') &&
             !normalizedRelativePath.includes('/_'); // 忽略以 _ 开头的文件和目录
    });
  }

  /**
   * 注册单个路由文件
   */
  private async registerRouteFile(filePath: string): Promise<void> {
    try {
      const module = await import(filePath) as RouteModule;

      // 跳过禁用的模块
      if (module.config?.enabled === false) {
        return;
      }

      // 根据导出模式注册路由
      if (module.register && typeof module.register === 'function') {
        // 自定义注册函数
        module.register(this.app);
      } else if (module.builder instanceof HonoApiRouteBuilder) {
        // HonoApiRouteBuilder 实例（已经包含 prefix）
        module.builder.register(this.app);
      } else if (module.default instanceof Hono) {
        // 默认导出的 Hono 实例
        const routePath = await this.generateRoutePath(filePath);
        (this.app as any).route(routePath, module.default);
      } else if (module.routes instanceof Hono) {
        // 命名导出的 routes
        const routePath = await this.generateRoutePath(filePath);
        (this.app as any).route(routePath, module.routes);
      } else {
        // 尝试查找其他可能的 Hono 实例导出
        const honoInstance = this.findHonoInstance(module);
        if (honoInstance) {
          const routePath = await this.generateRoutePath(filePath);
          (this.app as any).route(routePath, honoInstance);
        } else {
          console.warn(`⚠️  未找到有效的路由导出: ${filePath}`);
        }
      }

      this.registeredRoutes.set(filePath, module);
    } catch (error) {
      console.warn(`❌ 注册路由文件失败 ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * 从文件路径生成路由路径（Windows 兼容）
   */
  private async generateRoutePath(filePath: string): Promise<string> {
    const path = await import('path');

    // 规范化路径
    const normalizedRoutesDir = path.resolve(this.options.routesDir);
    const normalizedFilePath = path.resolve(filePath);

    // 获取相对路径
    const relativePath = path.relative(normalizedRoutesDir, normalizedFilePath);

    // 统一使用 POSIX 风格的路径分隔符
    let routePath = relativePath
      .replace(/\\/g, '/') // Windows 反斜杠转为正斜杠
      .replace(/\.(ts|js)$/, '') // 移除扩展名
      .replace(/\/index$/, '') // 移除 /index
      .replace(/\[([^\]]+)\]/g, ':$1'); // 转换 [param] 为 :param

    // 处理特殊路由模式
    if (routePath === '' || routePath === '.') routePath = '/';
    if (!routePath.startsWith('/')) routePath = '/' + routePath;

    return routePath;
  }

  /**
   * 查找模块中的 Hono 实例
   */
  private findHonoInstance(module: any): Hono | null {
    // 常见的导出名称
    const commonNames = [
      'routes', 'router', 'app', 'default',
      'healthRoutes', 'userRoutes', 'apiRoutes'
    ];

    for (const name of commonNames) {
      if (module[name] instanceof Hono) {
        return module[name];
      }
    }

    // 遍历所有导出，查找 Hono 实例
    for (const [key, value] of Object.entries(module)) {
      if (value instanceof Hono) {
        return value;
      }
    }

    return null;
  }

  /**
   * 获取已注册的路由信息
   */
  getRegisteredRoutes(): Array<{ path: string; module: RouteModule }> {
    return Array.from(this.registeredRoutes.entries()).map(([path, module]) => ({
      path,
      module
    }));
  }
}

/**
 * 简化的自动注册函数
 */
export async function autoRegisterRoutes(
  app: Hono,
  routesDir: string,
  excludePatterns?: string[]
): Promise<AutoRouter> {
  const router = new AutoRouter(app, { routesDir, excludePatterns });
  await router.registerRoutes();
  return router;
}

/**
 * 创建路由模块的辅助函数
 */
export function createRouteModule(
  routes: Hono | HonoApiRouteBuilder,
  config?: { enabled?: boolean }
): RouteModule {
  if (routes instanceof HonoApiRouteBuilder) {
    return {
      builder: routes,
      config: { enabled: true, ...config }
    };
  } else {
    return {
      routes,
      config: { enabled: true, ...config }
    };
  }
}
