/**
 * Auto-discoverable user routes using convention-over-configuration
 * This file will be automatically registered at /auto-users
 */

import { createRouteModule } from '../server/core/auto-router';
import { HonoApiRouteBuilder } from '../server/builders/hono';
import { users } from '../server/db/schema/auth';
import { z } from 'zod';

// Define schemas
const userSchemas = {
  POST: z.object({
    name: z.string().min(1, 'Name is required').max(100),
    email: z.string().email('Invalid email format'),
  }),
  PUT: z.object({
    name: z.string().min(1, 'Name is required').max(100).optional(),
    email: z.string().email('Invalid email format').optional(),
  }),
};

// Create the route builder with prefix
const userRouteBuilder = new HonoApiRouteBuilder()
  .resource('users', users)
  .prefix('/api/auto-users') // 构建器自己管理 prefix
  .schema(userSchemas)
  .filterable({
    name: {
      schema: z.string(),
      operators: ['eq', 'like']
    },
    email: {
      schema: z.string(),
      operators: ['eq']
    },
    createdAt: {
      schema: z.number(),
      operators: ['gt', 'lt', 'between']
    },
  })
  .sortable(['name', 'email', 'createdAt'])
  .searchable(['name', 'email'])
  .pagination({
    defaultLimit: 20,
    maxLimit: 100
  });

// Export using the auto-router convention
export default createRouteModule(userRouteBuilder, {
  enabled: true 
});
