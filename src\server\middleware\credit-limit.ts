import type { Middleware } from "../types";
import { ResponseUtils } from "../utils/response";
import { db } from "@/server/db";
import { users } from "@/server/db/schema/auth";
import { eq } from "drizzle-orm";

interface CreditConfig {
    cost: number | ((context: any) => Promise<number>);  // 固定消耗或动态计算消耗
    rateLimit?: boolean;                                 // 是否需要限速
}

interface CreditLimitOptions {
    GET?: CreditConfig;
    POST?: CreditConfig;
    PUT?: CreditConfig;
    DELETE?: CreditConfig;
    skipFailedRequests?: boolean;     // 是否跳过失败的请求
    skipSuccessfulRequests?: boolean; // 是否跳过成功的请求
}

/**
 * 计算操作所需的积分
 */
async function calculateCredits(context: any, config: CreditConfig): Promise<number> {
    if (typeof config.cost === 'function') {
        return await config.cost(context);
    }
    return config.cost;
}

export function createCreditLimitMiddleware(options: CreditLimitOptions = {}): Middleware {
    return async (context, next) => {
        try {
            const method = context.req.method as keyof CreditLimitOptions;
            const methodConfig = options[method] as CreditConfig | undefined;

            if (!methodConfig) {
                await next();
                return;
            }

            // 检查rate-limit中间件
            if (methodConfig.rateLimit) {
                if (!context.middlewareManager?.hasMiddleware('rate-limit')) {
                    throw ResponseUtils.badRequest(
                        'Rate limit middleware is required but not registered'
                    );
                }
            }

            // 获取用户信息
            const user = context.state.get("user");
            if (!user) {
                throw ResponseUtils.unauthorized("User not found");
            }

            // 计算所需积分
            const requiredCredits = await calculateCredits(context, methodConfig);

            // 检查用户是否有足够的积分
            if (user.credits < requiredCredits) {
                throw ResponseUtils.forbidden("Insufficient credits");
            }

            // 执行操作
            await next();

            // 根据请求结果决定是否扣除积分
            const status = context.response?.status || 500;
            const shouldDeductCredits = !(
                (options.skipSuccessfulRequests && status >= 200 && status < 300) ||
                (options.skipFailedRequests && status >= 400)
            );

            if (shouldDeductCredits) {
                // 更新用户积分
                await db
                    .update(users)
                    .set({ credits: (user.credits || 0) - requiredCredits })
                    .where(eq(users.id, user.id));
            }

        } catch (error) {
            if (error instanceof Response) {
                throw error;
            }
            throw ResponseUtils.internalError("Credit limit middleware error");
        }
    };
} 