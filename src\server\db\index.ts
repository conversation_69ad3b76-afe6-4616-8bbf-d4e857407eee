// 数据库连接适配器
import { createClient } from '@libsql/client';
import { drizzle } from 'drizzle-orm/libsql';
import * as schema from './schema';

// 数据库配置
const dbConfig = {
  url: process.env.TURSO_DATABASE_URL || 'file:local.db',
  authToken: process.env.TURSO_AUTH_TOKEN,
};

// 创建 libSQL 客户端
const client = createClient(dbConfig);

// 创建 Drizzle 实例 (用于 ORM 操作)
export const db = drizzle(client, { schema });

// 导出原始客户端 (用于直接 SQL 操作)
export const rawClient = client;

// 导出 schema
export * from './schema';
