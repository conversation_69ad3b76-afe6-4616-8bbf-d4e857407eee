import { db } from "@/server/db";
import { sql, SQL, getTableColumns, or } from "drizzle-orm";
import type { SQLiteTableWithColumns, SQLiteColumn, SQLiteTable } from "drizzle-orm/sqlite-core";

import {
  eq,
  and,
  desc,
  asc,
  gt,
  gte,
  lt,
  lte,
  like,
  inArray,
  between,
} from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import type { TransactionCallback, RelationsConfig } from "../types";
import { ResponseUtils } from "../utils/response";
interface SelectQueryOptions {
  select?: string[];
  where?: Record<string, any>;
  customWhere?: SQL<unknown> | null;
  orderBy?: Array<{ field: string; order: "asc" | "desc" }>;
  limit?: number;
  offset?: number;
  with?: RelationsConfig;
  extras?: Record<string, SQL.Aliased<unknown>>;
  detail?: boolean;
}

export class QueryBuilder {
  db: any;

  constructor() {
    this.db = db;
    if (!this.db)
      throw ResponseUtils.internalError("Database connection not initialized");
  }

  /**
   * Start a new transaction
   */
  async transaction<T>(callback: TransactionCallback<T>): Promise<T> {
    // return callback(this.db);
    return this.db.transaction(callback);
  }

  async getCount(
    table: SQLiteTableWithColumns<any>,
    whereClause: Record<string, any>,
    customWhere?: SQL<unknown> | null,
    extras?: Record<string, SQL.Aliased<unknown>>
  ) {
    const baseWhereCondition = this.buildWhereClause(table, whereClause, customWhere, extras);

    const query = this.db
      .select({ count: sql`count(*)` })
      .from(table)
      .where(baseWhereCondition);

    const [{ count }] = await query;
    return Number(count);
  }

  private getUniqueFields(table: SQLiteTableWithColumns<any>) {
    return Object.keys(getTableColumns(table)).filter(field => table[field].isUnique);
  }

  private conflictUpdateAllExcept<
    T extends SQLiteTable,
    E extends (keyof T["$inferInsert"])[],
  >(table: T, except: E) {
    const columns = getTableColumns(table);
    const updateColumns = Object.entries(columns).filter(
      ([col]) => !except.includes(col as keyof typeof table.$inferInsert),
    );

    return updateColumns.reduce(
      (acc, [colName, table]) => ({
        ...acc,
        [colName]: sql.raw(`excluded.${table.name}`),
      }),
      {},
    ) as Omit<Record<keyof typeof table.$inferInsert, SQL>, E[number]>;
  }

  async buildInsertQuery<T extends Record<string, any>>(
    table: SQLiteTableWithColumns<any>,
    data: T,
    options: {
      onConflict?: 'doNothing' | 'doUpdate' | 'throwError';
    } = {},
    tx?: any
  ) {
    const db = tx || this.db;
    const query = db
      .insert(table)
      .values({
        id: uuidv4(),
        ...data,
        createdAt: new Date().getTime(),
        updatedAt: new Date().getTime(),
      })

    const uniqueFields = this.getUniqueFields(table);
    const { onConflict = 'throwError' } = options; // 默认让约束自然抛出错误
    console.log("uniqueFields", uniqueFields, onConflict);

    // 只有明确指定冲突处理策略时才应用
    if (uniqueFields.length > 0 && onConflict !== 'throwError') {
      const onConflictMethod = onConflict === 'doNothing' ? 'onConflictDoNothing' : 'onConflictDoUpdate';
      const onConflictDoUpdateParams = {
        target: uniqueFields.map(field => table[field]),
        set: this.conflictUpdateAllExcept(table, uniqueFields) // 使用正确的方法构造更新参数
      };

      return query[onConflictMethod](onConflictMethod === 'onConflictDoNothing' ? undefined : onConflictDoUpdateParams)
        .returning();
    }

    // 默认情况下让约束自然抛出错误
    return query.returning();
  }
  // 批量插入
  async buildBatchInsertQuery<T extends Record<string, any>>(
    table: SQLiteTableWithColumns<any>,
    records: T[],
    options: {
      batchSize?: number;
      timestamps?: boolean;
      onConflict?: 'doNothing' | 'doUpdate' | 'throwError';
    } = {},
    tx?: any
  ) {
    const db = tx || this.db;
    const {
      batchSize = 1000,
      timestamps = true,
      onConflict = 'throwError' // 默认让约束自然抛出错误
    } = options;


    // 2. 准备数据
    const now = new Date().getTime();
    const preparedRecords = records.map(record => ({
      id: uuidv4(),
      ...record,
      ...(timestamps ? {
        createdAt: now,
        updatedAt: now
      } : {})
    }));

    // 3. 分批处理
    const results = [];
    const uniqueFields = this.getUniqueFields(table);
    try {
      for (let i = 0; i < preparedRecords.length; i += batchSize) {
        const batch = preparedRecords.slice(i, i + batchSize);
        const query = db
          .insert(table)
          .values(batch)
        let batchResults = [];

        // 只有明确指定冲突处理策略时才应用
        if (uniqueFields.length > 0 && onConflict !== 'throwError') {
          const onConflictMethod = onConflict === 'doNothing' ? 'onConflictDoNothing' : 'onConflictDoUpdate';
          const onConflictDoUpdateParams = {
            target: uniqueFields.map(field => table[field]),
            set: this.conflictUpdateAllExcept(table, uniqueFields)
          };

          batchResults = await query[onConflictMethod](onConflictMethod === 'onConflictDoNothing' ? undefined : onConflictDoUpdateParams).returning();
        } else {
          // 默认情况下让约束自然抛出错误
          batchResults = await query.returning();
        }

        results.push(...batchResults);
      }
    } catch (error) {
      throw ResponseUtils.badRequest("Failed to insert records");
    }

    return results;
  }
  // 批量更新
  async buildUpdateQuery<T extends Record<string, any>>(
    table: SQLiteTableWithColumns<any>,
    id: string,
    data: T,
    tx?: any
  ) {
    const db = tx || this.db;
    return db
      .update(table)
      .set({
        ...data,
        updatedAt: new Date().getTime(),
      })
      .where(eq(table.id, id))
      .returning();
  }
  // 批量删除
  async buildBatchDeleteQuery<T extends Record<string, any>>(
    table: SQLiteTableWithColumns<any>,
    ids: string[],
    tx?: any
  ) {
    const db = tx || this.db;
    return db.delete(table).where(inArray(table.id, ids)).returning();
  }


  async buildBatchUpdateQuery<T extends Record<string, any>>(
    table: SQLiteTableWithColumns<any>,
    records: Array<{ id: string } & T>,
    options: {
      batchSize?: number;
      timestamps?: boolean;
    } = {},
    tx?: any
  ) {
    const db = tx || this.db;
    const {
      batchSize = 1000,
      timestamps = true
    } = options;

    const results = [];
    const now = new Date().getTime();

    // 分批处理更新
    for (let i = 0; i < records.length; i += batchSize) {
      const batch = records.slice(i, i + batchSize);

      // 并行处理每条记录的更新
      const batchResults = await Promise.all(
        batch.map(async record => {
          const { id, ...updateData } = record;

          return db
            .update(table)
            .set({
              ...updateData,
              ...(timestamps ? { updatedAt: now } : {})
            })
            .where(eq(table.id, id))
            .returning();
        })
      );

      results.push(...batchResults.flat());
    }

    return results;
  }

  async buildDeleteQuery(
    table: SQLiteTableWithColumns<any>,
    id: string,
    tx?: any
  ) {
    const db = tx || this.db;
    return db.delete(table).where(eq(table.id, id)).returning();
  }

  private buildWhereClause(
    table: SQLiteTableWithColumns<any>,
    where: Record<string, any>,
    customWhere?: SQL<unknown> | null,
    extras?: Record<string, SQL.Aliased<unknown>>
  ): SQL<unknown> | undefined {
    const conditions: SQL<unknown>[] = [];

    // Add customWhere clause if provided and not null
    if (customWhere !== undefined && customWhere !== null) {
      conditions.push(customWhere);
    }

    for (const [field, value] of Object.entries(where)) {
      if (field === "_search") {
        conditions.push(value as SQL<unknown>);
        continue;
      }
      let column = (table[field] || extras?.[field]) as SQLiteColumn<any, any>;
      if (!column) continue;
      // 如果 column 是 SQL.Aliased 类型，则获取其原始 SQL
      if (column instanceof SQL.Aliased) {
        column = column.getSQL() as any;
      }
      // Handle operator-based conditions
      if (typeof value === "object" && value !== null) {
        const [operator, operatorValue] = Object.entries(value)[0];
        switch (operator) {
          case "eq":
            conditions.push(eq(column, operatorValue));
            break;
          case "gt":
            conditions.push(gt(column, Number(operatorValue)));
            break;
          case "gte":
            conditions.push(gte(column, Number(operatorValue)));
            break;
          case "lt":
            conditions.push(lt(column, Number(operatorValue)));
            break;
          case "lte":
            conditions.push(lte(column, Number(operatorValue)));
            break;
          case "like":
            conditions.push(like(column, `%${operatorValue}%`));
            break;
          case "in":
            conditions.push(inArray(column, operatorValue as any[]));
            break;
          case "between":
            if (typeof operatorValue === 'string' && operatorValue.includes(',')) {
              const [min, max] = operatorValue.split(',');
              conditions.push(between(column, min, max));
            }
            break;
          default:
            // Skip unknown operators
            continue;
        }
      } else {
        // Fallback to equality for direct values (shouldn't happen with new structure)
        conditions.push(eq(column, value));
      }
    }

    return conditions.length > 0 ? and(...conditions) : undefined;
  }

  private buildOrderByClause(
    table: SQLiteTableWithColumns<any>,
    sortFields: Array<{ field: string; order: "asc" | "desc" }> = [],
    extras?: Record<string, SQL.Aliased<unknown>>
  ) {
    if (sortFields.length === 0) return [];

    return sortFields.map(({ field, order }) => {
      const column = table[field];
      if (!column) {
        if (extras?.hasOwnProperty(field)) {
          // 将整个排序表达式作为原始 SQL 字符串处理
          return sql.raw(`${field} ${order === 'desc' ? 'DESC' : 'ASC'}`);
        }
        throw ResponseUtils.badRequest(`Invalid sort field: ${field}`);
      }
      return order === "desc" ? desc(column) : asc(column);
    });
  }

  async buildSelectQuery(
    table: SQLiteTableWithColumns<any>,
    resourceName: string,
    options: SelectQueryOptions
  ) {
    const {
      select = [],
      where = {},
      customWhere,
      orderBy = [],
      limit,
      offset,
      extras,
      with: relations,
      detail = false,
    } = options;

    // Build query config
    const findManyConfig: any = {};

    // Handle select fields
    if (select?.length) {
      findManyConfig.columns = Object.fromEntries(
        select.map((field) => [field, true])
      );
    }

    // Handle where conditions
    const whereClause = this.buildWhereClause(table, where, customWhere, extras);
    if (whereClause) {
      findManyConfig.where = whereClause;
    }

    // Handle ordering
    const orderByFields = this.buildOrderByClause(table, orderBy, extras);
    if (orderByFields.length > 0) {
      findManyConfig.orderBy = orderByFields;
    }

    // Handle pagination
    if (typeof limit === "number") {
      findManyConfig.limit = limit;
    }
    if (typeof offset === "number") {
      findManyConfig.offset = offset;
    }

    if (extras) {
      findManyConfig.extras = extras;
    }

    // Handle relations
    if (relations) {
      findManyConfig.with = relations;
    }

    // console.log("findManyConfig", JSON.stringify(findManyConfig));

    // Use findFirst for detail queries, findMany for list queries
    return detail
      ? await this.db.query[resourceName].findFirst(findManyConfig)
      : await this.db.query[resourceName].findMany(findManyConfig);
  }
}

export const createQueryBuilder = () => new QueryBuilder();
