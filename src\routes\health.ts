import { Hono } from 'hono';
import { checkDatabaseHealth } from '../config/database';

export const healthRoutes = new Hono();

// Basic health check
healthRoutes.get('/', async (c) => {
  const dbHealthy = await checkDatabaseHealth();
  
  const health = {
    status: dbHealthy ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    database: dbHealthy ? 'connected' : 'disconnected',
    version: '1.0.0'
  };

  const statusCode = dbHealthy ? 200 : 503;
  return c.json(health, statusCode);
});

// Detailed health check
healthRoutes.get('/detailed', async (c) => {
  const dbHealthy = await checkDatabaseHealth();
  
  const health = {
    status: dbHealthy ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    database: {
      status: dbHealthy ? 'connected' : 'disconnected',
      url: process.env.TURSO_DATABASE_URL ? 'configured' : 'using local file'
    },
    environment: {
      node_version: process.version,
      platform: process.platform,
      arch: process.arch
    }
  };

  const statusCode = dbHealthy ? 200 : 503;
  return c.json(health, statusCode);
});
