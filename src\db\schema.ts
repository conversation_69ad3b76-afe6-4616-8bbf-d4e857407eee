import { sql, relations } from "drizzle-orm";
import { sqliteTable, text, integer, index } from "drizzle-orm/sqlite-core";

// 闲鱼元关键词表 - 用于存储主要搜索关键词
export const goofish_meta_keywords = sqliteTable("goofish_meta_keywords", {
    id: text("id").primaryKey(),
    keyword: text("keyword").notNull().unique(), // 关键词
    last_crawl_date: integer("last_crawl_date", { mode: "number" }), // 最后爬取时间
    level: integer("level", { mode: "number" }).default(1), // 关键词优先级
    created_at: integer("created_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 创建时间
    updated_at: integer("updated_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 更新时间
}, (table) => [
    index("idx_goofish_keywords_last_crawl_date").on(table.last_crawl_date),
]);

// 闲鱼搜索关键词表 - 记录实际搜索使用的关键词
export const goofish_search_keywords = sqliteTable("goofish_search_keywords", {
    id: text("id").primaryKey(),
    keyword: text("keyword").unique().notNull(), // 实际搜索的关键词
    level: integer("level", { mode: "number" }).default(1), // 关键词优先级
    meta_keyword_id: text("meta_keyword_id").notNull().references(() => goofish_meta_keywords.id), // 关联的元关键词ID
    created_at: integer("created_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 创建时间
    last_crawl_date: integer("last_crawl_date", { mode: "number" }), // 最后爬取时间
}, (table) => [
    index("idx_goofish_search_keywords_keyword").on(table.keyword)
]);

export const goofish_meta_keywordsRelations = relations(goofish_meta_keywords, ({ many }) => ({
    search_keywords: many(goofish_search_keywords), // 一个元关键词可以对应多个搜索关键词
}));

export const goofish_search_keywordsRelations = relations(goofish_search_keywords, ({ one, many }) => ({
    meta_keyword: one(goofish_meta_keywords, {
        fields: [goofish_search_keywords.meta_keyword_id],
        references: [goofish_meta_keywords.id],
    }),
    item_keywords: many(goofish_item_keywords), // 一个搜索关键词可以关联多个商品
}));

// 闲鱼商品表 - 存储商品基本信息
export const goofish_items = sqliteTable("goofish_items", {
    id: text("id").primaryKey(),
    item_id: text("item_id").notNull().unique(), // 平台商品ID
    pic_url: text("pic_url"), // 商品图片URL
    title: text("title").notNull(), // 商品标题
    price: integer("price", { mode: "number" }).notNull(), // 商品价格（单位：分）
    seller_id: text("seller_id").references(() => goofish_sellers.seller_id), // 卖家ID
    seller_nick: text("seller_nick"), // 卖家昵称
    seller_rate: integer("seller_rate"), // 卖家评分
    want_num: integer("want_num", { mode: "number" }).default(0), // 想要人数
    browse_num: integer("browse_num", { mode: "number" }).default(0), // 浏览人数
    banned: integer("banned", { mode: "number" }).default(0), // 是否被封禁 0:否 1:是
    banned_date: integer("banned_date", { mode: "number" }), // 封禁时间
    collect_num: integer("collect_num", { mode: "number" }).default(0), // 收藏人数
    publish_time: integer("publish_time", { mode: "number" }), // 发布时间
    quantity: integer("quantity", { mode: "number" }).default(0), // 库存数量
    area: text("area"), // 发货地区
    tags: text("tags"), // 商品标签
    item_url: text("item_url"), // 商品链接
    is_sold_out: integer("is_sold_out", { mode: "number" }).default(0), // 是否下架 0:否 1:是
    created_at: integer("created_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 创建时间
    updated_at: integer("updated_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 更新时间
}, (table) => [
    index("idx_goofish_items_price").on(table.price),
    index("idx_goofish_items_publish_time").on(table.publish_time),
    index("idx_goofish_items_seller_id").on(table.seller_id),
]);

export const goofish_itemsRelations = relations(goofish_items, ({ many, one }) => ({
    item_keywords: many(goofish_item_keywords), // 一个商品可以对应多个关键词关联
    history: many(goofish_item_history), // 一个商品可以有多条历史记录
    seller: one(goofish_sellers, { // 一个商品对应一个卖家
        fields: [goofish_items.seller_id],
        references: [goofish_sellers.seller_id],
    }),
}));

// 闲鱼商品历史记录表 - 追踪商品价格和想要人数变化
export const goofish_item_history = sqliteTable("goofish_item_history", {
    id: text("id").primaryKey(),
    item_id: text("item_id").notNull().references(() => goofish_items.id), // 商品表ID
    task_group_target_id: text("task_group_target_id").notNull().references(() => goofish_task_group_targets.id), // 任务组目标ID
    want_num: integer("want_num", { mode: "number" }).default(0), // 记录时的想要人数
    collect_num: integer("collect_num", { mode: "number" }).default(0), // 记录时的收藏人数
    quantity: integer("quantity", { mode: "number" }).default(0), // 库存数量
    browse_num: integer("browse_num", { mode: "number" }).default(0), // 浏览人数
    record_date: integer("record_date", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 记录时间
});

export const goofish_item_historyRelations = relations(goofish_item_history, ({ one }) => ({
    item: one(goofish_items, {
        fields: [goofish_item_history.item_id],
        references: [goofish_items.id],
    }),
    task_group_target: one(goofish_task_group_targets, {
        fields: [goofish_item_history.task_group_target_id],
        references: [goofish_task_group_targets.id],
    })
}));

// 新增关联表：闲鱼商品与搜索关键词的关联表
export const goofish_item_keywords = sqliteTable("goofish_item_keywords", {
    id: text("id").primaryKey(),
    item_id: text("item_id").notNull().references(() => goofish_items.id), // 商品ID
    search_keyword_id: text("search_keyword_id").notNull().references(() => goofish_search_keywords.id), // 搜索关键词ID
    created_at: integer("created_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 创建时间
}, (table) => [
    index("idx_goofish_item_keywords_item_id").on(table.item_id),
    index("idx_goofish_item_keywords_search_keyword_id").on(table.search_keyword_id),
    index("unique_goofish_item_keyword").on(table.item_id, table.search_keyword_id),
]);

export const goofish_item_keywordsRelations = relations(goofish_item_keywords, ({ one }) => ({
    item: one(goofish_items, {
        fields: [goofish_item_keywords.item_id],
        references: [goofish_items.id],
    }),
    keyword: one(goofish_search_keywords, {
        fields: [goofish_item_keywords.search_keyword_id],
        references: [goofish_search_keywords.id],
    }),
}));

// 闲鱼卖家表 - 存储卖家信息
export const goofish_sellers = sqliteTable('goofish_sellers', {
    id: text("id").primaryKey(),
    seller_nick: text("seller_nick"), // 卖家昵称
    seller_id: text("seller_id").notNull().unique(), // 平台卖家ID
    shop_score: integer("shop_score", { mode: "number" }), // 店铺评分
    ios_verify: integer("ios_verify", { mode: "number" }).default(0), // 是否通过iOS验证
    praise_ratio: integer("praise_ratio", { mode: "number" }), // 好评率
    followers: integer("followers", { mode: "number" }).default(0), // 粉丝数
    attention_privacy_protected: integer("attention_privacy_protected", { mode: "number" }).default(0), // 是否开启关注隐私保护
    items_count: integer("items_count", { mode: "number" }).default(0), // 在售商品数量
    evaluates: integer("evaluates", { mode: "number" }).default(0), // 评价总数
    has_sold_num: integer("has_sold_num", { mode: "number" }).default(0), // 已售出商品数量
    reply_in_24h_ratio: integer("reply_in_24h_ratio", { mode: "number" }), // 24小时回复率
    reply_interval: integer("reply_interval", { mode: "number" }), // 平均回复时间（分钟）
    register_time: integer("register_time", { mode: "number" }), // 注册时间
    seller_good_remark_cnt: integer("seller_good_remark_cnt", { mode: "number" }).default(0), // 好评数量
    seller_bad_remark_cnt: integer("seller_bad_remark_cnt", { mode: "number" }).default(0), // 差评数量
    created_at: integer("created_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 创建时间
    updated_at: integer("updated_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 更新时间
}, (table) => [
    index("idx_goofish_sellers_seller_id").on(table.seller_id),
    index("idx_goofish_sellers_register_time").on(table.register_time),
]);

// 闲鱼卖家历史记录表 - 追踪卖家数据变化
export const goofish_seller_history = sqliteTable("goofish_seller_history", {
    id: text("id").primaryKey(),
    seller_id: text("seller_id").notNull().references(() => goofish_sellers.seller_id), // 卖家ID
    task_group_target_id: text("task_group_target_id").notNull().references(() => goofish_task_group_targets.id), // 任务组目标ID
    shop_score: integer("shop_score", { mode: "number" }), // 店铺评分
    praise_ratio: integer("praise_ratio", { mode: "number" }), // 好评率
    followers: integer("followers", { mode: "number" }), // 粉丝数
    items_count: integer("items_count", { mode: "number" }), // 在售商品数量
    evaluates: integer("evaluates", { mode: "number" }), // 评价总数
    has_sold_num: integer("has_sold_num", { mode: "number" }), // 已售出商品数量
    reply_in_24h_ratio: integer("reply_in_24h_ratio", { mode: "number" }), // 24小时回复率
    seller_good_remark_cnt: integer("seller_good_remark_cnt", { mode: "number" }), // 好评数量
    seller_bad_remark_cnt: integer("seller_bad_remark_cnt", { mode: "number" }), // 差评数量
    banned: integer("banned", { mode: "number" }).default(0), // 是否被封禁 0:否 1:是
    banned_date: integer("banned_date", { mode: "number" }), // 封禁时间
    record_date: integer("record_date", { mode: "number" }).default(sql`(unixepoch() * 1000)`), // 记录时间
}, (table) => [
    index("idx_goofish_seller_history_seller_id").on(table.seller_id),
    index("idx_goofish_seller_history_record_date").on(table.record_date),
]);

export const goofish_sellersRelations = relations(goofish_sellers, ({ many }) => ({
    history: many(goofish_seller_history), // 一个卖家可以有多条历史记录
    items: many(goofish_items), // 一个卖家可以有多个商品
}));

export const goofish_seller_historyRelations = relations(goofish_seller_history, ({ one }) => ({
    seller: one(goofish_sellers, {
        fields: [goofish_seller_history.seller_id],
        references: [goofish_sellers.seller_id],
    }),
    task_group_target: one(goofish_task_group_targets, {
        fields: [goofish_seller_history.task_group_target_id],
        references: [goofish_task_group_targets.id],
    })
}));

// 抓取任务表 - 管理需要定时抓取的商品和卖家
export const goofish_crawl_tasks = sqliteTable("goofish_crawl_tasks", {
    id: text("id").primaryKey(),
    target_type: text("target_type").notNull(), // 抓取目标类型：'item' 或 'seller' 或 'keyword'
    target_id: text("target_id").notNull().unique(), // 目标ID（商品ID或卖家ID或关键词ID）
    interval: integer("interval", { mode: "number" }).notNull(), // 抓取间隔（分钟）
    last_crawl_time: integer("last_crawl_time", { mode: "number" }).default(0), // 最后抓取时间
    status: text("status").default("active"), // 任务状态：active/paused/completed
    priority: integer("priority", { mode: "number" }).default(1), // 任务优先级
    created_at: integer("created_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`),
    updated_at: integer("updated_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`),
}, (table) => [
    index("idx_goofish_crawl_tasks_target").on(table.target_type, table.target_id),
    index("idx_goofish_crawl_tasks_last_crawl").on(table.last_crawl_time),
    index("idx_goofish_crawl_tasks_status").on(table.status),
]);

export const goofish_crawl_tasksRelations = relations(goofish_crawl_tasks, ({ one }) => ({
    seller: one(goofish_sellers, {
        fields: [goofish_crawl_tasks.target_id],
        references: [goofish_sellers.id],
    }),

    item: one(goofish_items, {
        fields: [goofish_crawl_tasks.target_id],
        references: [goofish_items.id],
    }),
}));

// 闲鱼任务组表 - 管理抓取任务组
export const goofish_task_groups = sqliteTable("goofish_task_groups", {
    id: text("id").primaryKey(),
    name: text("name").notNull().unique(), // 任务组名称
    description: text("description"), // 任务组描述
    target_type: text("target_type").notNull(), // 目标类型：'item' 或 'seller'
    status: text("status").default("active").notNull(), // 任务组状态：active/paused/completed
    last_crawl_time: integer("last_crawl_time", { mode: "number" }).default(0), // 最后抓取时间
    created_at: integer("created_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`),
    updated_at: integer("updated_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`),
}, (table) => [
    index("idx_goofish_task_groups_status").on(table.status),
    index("idx_goofish_task_groups_target_type").on(table.target_type),
]);

// 任务组与目标的关联表
export const goofish_task_group_targets = sqliteTable("goofish_task_group_targets", {
    id: text("id").primaryKey(),
    group_id: text("group_id").notNull().references(() => goofish_task_groups.id), // 关联的任务组ID
    target_id: text("target_id").notNull(), // 目标ID（商品ID或商家ID）
    status: text("status").default("pending").notNull(), // 处理状态：pending/processing/completed/failed
    last_crawl_time: integer("last_crawl_time", { mode: "number" }).default(0), // 最后抓取时间
    created_at: integer("created_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`),
    updated_at: integer("updated_at", { mode: "number" }).default(sql`(unixepoch() * 1000)`),
}, (table) => [
    index("idx_goofish_task_group_targets_group").on(table.group_id),
    index("idx_goofish_task_group_targets_target").on(table.target_id),
    index("idx_goofish_task_group_targets_status").on(table.status),
    index("unique_goofish_task_group_target").on(table.group_id, table.target_id),
]);

// 设置关系
export const goofish_task_groupsRelations = relations(goofish_task_groups, ({ many }) => ({
    targets: many(goofish_task_group_targets), // 一个任务组可以包含多个目标
}));

export const goofish_task_group_targetsRelations = relations(goofish_task_group_targets, ({ one, many }) => ({
    group: one(goofish_task_groups, {
        fields: [goofish_task_group_targets.group_id],
        references: [goofish_task_groups.id],
    }),
    item: one(goofish_items, {
        fields: [goofish_task_group_targets.target_id],
        references: [goofish_items.id],
    }),
    seller: one(goofish_sellers, {
        fields: [goofish_task_group_targets.target_id],
        references: [goofish_sellers.id],
    }),
    item_history: many(goofish_item_history),
    seller_history: many(goofish_seller_history),
}));
