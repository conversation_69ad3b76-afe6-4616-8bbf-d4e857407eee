import type { Context, Hono } from "hono";
import type { ApiContext, HttpMethod, } from "../types";
import { QueryUtils, ResponseUtils, ValidationUtils } from "../utils";
import { BaseApiRouteBuilder } from "./base-route-builder";

/**
 * Hono 框架的路由构建器
 * 继承基类，专门为 Hono 框架优化
 */
export class HonoApiRouteBuilder extends BaseApiRouteBuilder {

    private async handleRequest(c: Context): Promise<Response> {
        try {
            const method = c.req.method as HttpMethod;
            const url = new URL(c.req.url);
            const routeParams = c.req.param();

            // 1. 验证路由参数
            const validatedParams = ValidationUtils.validateRouteParams(
                routeParams || {},
                this.paramsFields
            );
            if (validatedParams instanceof Response) {
                return validatedParams;
            }

            // 2. 验证请求体
            let validatedBody = {};
            let originalBody = {};

            if (['POST', 'PUT'].includes(method)) {
                try {
                    originalBody = await c.req.json();

                    // 使用现有的验证逻辑，但适配 Hono
                    const schema = this.schemas?.[method as 'POST' | 'PUT'];
                    if (schema) {
                        const validationResult = await schema.safeParseAsync(originalBody);
                        if (!validationResult.success) {
                            return ResponseUtils.badRequest('Validation failed', {
                                errors: validationResult.error.errors
                            });
                        }
                        validatedBody = validationResult.data;
                    } else {
                        validatedBody = originalBody;
                    }
                } catch (error) {
                    if (['POST', 'PUT'].includes(method)) {
                        return ResponseUtils.badRequest('Invalid JSON body');
                    }
                }
            } else if (method === 'DELETE') {
                // DELETE 请求检查是否有 body（批量操作需要）
                try {
                    originalBody = await c.req.json();
                    // 如果是批量删除（有 batch 字段），需要验证
                    if (originalBody && typeof originalBody === 'object' && 'batch' in originalBody) {
                        validatedBody = originalBody;
                    }
                } catch (error) {
                    // 没有 body 的 DELETE 请求是正常的
                    validatedBody = {};
                    originalBody = {};
                }
            }

            // 3. 解析查询参数
            const parsedQuery = QueryUtils.parseAllQueryParams(
                url.searchParams,
                this.filterableFields,
                Object.keys(this.paramsFields)
            );

            // 4. 验证查询参数
            const queryValidation = ValidationUtils.validateQueryParams(
                {
                    search: parsedQuery.search,
                    filter: parsedQuery.filter || {},
                    sortFields: parsedQuery.sortFields,
                    pagination: parsedQuery.pagination,
                },
                {
                    filterableFields: this.filterableFields,
                    sortableFields: this.sortableFields,
                    pagination: this.paginationConfig,
                }
            );
            if (queryValidation instanceof Response) {
                return queryValidation;
            }

            // 5. 构建 API 上下文
            const apiContext: ApiContext = {
                method,
                params: validatedParams,
                query: {
                    search: parsedQuery.search,
                    filter: parsedQuery.filter || {},
                    sort:
                        parsedQuery.sortFields.length > 0
                            ? parsedQuery.sortFields
                                .map((s) => `${s.field}:${s.order}`)
                                .join(",")
                            : undefined,
                    page: parsedQuery.pagination.page || 1,
                    limit: Math.min(
                        parsedQuery.pagination.limit || this.paginationConfig.defaultLimit,
                        this.paginationConfig.maxLimit
                    ),
                },
                originalQuery: url.searchParams,
                originalBody: originalBody,
                body:
                    method === "POST"
                        ? { ...validatedParams, ...validatedBody }
                        : validatedBody,
            };

            // 检查是否为批量操作请求
            const isBatchOperation = apiContext.body && typeof apiContext.body === 'object' && Array.isArray(apiContext.body.batch);

            // 如果是批量操作，使用专门的批量处理方法
            if (isBatchOperation && (["POST", 'PUT', 'DELETE'] as HttpMethod[]).includes(method)) {
                const result = await this.executeBatchOperation(method, apiContext);
                const transformedResult = await this.transformFunction(method, result, this.queryBuilder.db, apiContext).catch((err: Error | Response) => {
                    if (err instanceof Response) throw err;
                    throw ResponseUtils.internalError(err.message);
                });
                return ResponseUtils.success(transformedResult);
            }

            // 直接执行查询（Hono 暂不支持中间件系统，可以后续添加）
            const result = await this.executeQuery(method, apiContext);
            return ResponseUtils.success(result);

        } catch (error) {
            if (error instanceof Response) return error;
            return ResponseUtils.internalError(
                error instanceof Error ? error.message : "Internal server error"
            );
        }
    }

    /**
     * 注册路由到 Hono 应用
     * @param app Hono 应用实例
     * @returns 返回应用实例以支持链式调用
     */
    register(app: Hono) {
        const basePath = `${this.prefixPath}/${this.resourceName}`;

        console.log('🔍 注册路由:', basePath);

        // 注册 CRUD 路由
        app.get(basePath, this.handleRequest.bind(this));
        app.post(basePath, this.handleRequest.bind(this));
        app.put(`${basePath}/:${this.primaryKeyParam}`, this.handleRequest.bind(this));
        app.delete(`${basePath}/:${this.primaryKeyParam}`, this.handleRequest.bind(this));
        app.options(basePath, () => ResponseUtils.success(null));

        return app;
    }

    /**
     * 构建方法 - 返回注册函数
     * @returns 注册函数
     */
    build() {
        return this.register.bind(this);
    }
} 