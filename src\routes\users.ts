import { Hono } from 'hono';
import { z } from 'zod';
import { db } from '../config/database';

// 引入 Hono 构建器进行测试
import { HonoApiRouteBuilder } from '../server/builders/hono';
import { users } from '../server/db/schema/auth';

export const userRoutes = new Hono();

// Get all users
userRoutes.get('/', async (c) => {
  try {
    const result = await db.execute('SELECT * FROM users ORDER BY created_at DESC');
    return c.json({
      success: true,
      data: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch users'
    }, 500);
  }
});

// Get user by ID
userRoutes.get('/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const result = await db.execute({
      sql: 'SELECT * FROM users WHERE id = ?',
      args: [id]
    });

    if (result.rows.length === 0) {
      return c.json({
        success: false,
        error: 'User not found'
      }, 404);
    }

    return c.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return c.json({
      success: false,
      error: 'Failed to fetch user'
    }, 500);
  }
});

// Create new user
userRoutes.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const { name, email } = body;

    if (!name || !email) {
      return c.json({
        success: false,
        error: 'Name and email are required'
      }, 400);
    }

    const result = await db.execute({
      sql: 'INSERT INTO users (name, email) VALUES (?, ?) RETURNING *',
      args: [name, email]
    });

    return c.json({
      success: true,
      data: result.rows[0],
      message: 'User created successfully'
    }, 201);
  } catch (error) {
    console.error('Error creating user:', error);

    // Handle unique constraint violation
    if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
      return c.json({
        success: false,
        error: 'Email already exists'
      }, 409);
    }

    return c.json({
      success: false,
      error: 'Failed to create user'
    }, 500);
  }
});

// Update user
userRoutes.put('/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();
    const { name, email } = body;

    if (!name || !email) {
      return c.json({
        success: false,
        error: 'Name and email are required'
      }, 400);
    }

    const result = await db.execute({
      sql: 'UPDATE users SET name = ?, email = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? RETURNING *',
      args: [name, email, id]
    });

    if (result.rows.length === 0) {
      return c.json({
        success: false,
        error: 'User not found'
      }, 404);
    }

    return c.json({
      success: true,
      data: result.rows[0],
      message: 'User updated successfully'
    });
  } catch (error) {
    console.error('Error updating user:', error);

    // Handle unique constraint violation
    if (error instanceof Error && error.message.includes('UNIQUE constraint failed')) {
      return c.json({
        success: false,
        error: 'Email already exists'
      }, 409);
    }

    return c.json({
      success: false,
      error: 'Failed to update user'
    }, 500);
  }
});

// Delete user
userRoutes.delete('/:id', async (c) => {
  try {
    const id = c.req.param('id');

    const result = await db.execute({
      sql: 'DELETE FROM users WHERE id = ? RETURNING *',
      args: [id]
    });

    if (result.rows.length === 0) {
      return c.json({
        success: false,
        error: 'User not found'
      }, 404);
    }

    return c.json({
      success: true,
      message: 'User deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    return c.json({
      success: false,
      error: 'Failed to delete user'
    }, 500);
  }
});

// ===== 使用 Hono 构建器的示例 =====

// 定义用户验证 schema
const userSchema = {
  POST: z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Invalid email format'),
  }),
  PUT: z.object({
    name: z.string().min(1, 'Name is required').optional(),
    email: z.string().email('Invalid email format').optional(),
  }),
};

// 创建使用构建器的用户路由
export const userRoutesWithBuilder = new HonoApiRouteBuilder()
  .resource('users', users)
  .prefix('/api/v2') // 修正 prefix，应该包含完整路径
  .schema(userSchema)
  .filterable({
    name: {
      schema: z.string(),
      operators: ['eq', 'like']
    },
    email: {
      schema: z.string(),
      operators: ['eq']
    },
    createdAt: {
      schema: z.number(),
      operators: ['gt', 'lt', 'between']
    },
  })
  .sortable(['name', 'email', 'createdAt'])
  .searchable(['name', 'email'])
  .pagination({
    defaultLimit: 20,
    maxLimit: 100
  });

// 注册构建器路由到 Hono 应用
export function registerBuilderRoutes(app: Hono) {
  // 使用构建器的 register 方法自动注册所有 CRUD 路由
  userRoutesWithBuilder.register(app);

  console.log('✅ User routes registered with Hono API Builder');
  console.log('📍 Available endpoints:');
  console.log('  GET    /api/v2/users     - List users with filtering, sorting, pagination');
  console.log('  POST   /api/v2/users     - Create user');
  console.log('  GET    /api/v2/users/:id - Get user by ID');
  console.log('  PUT    /api/v2/users/:id - Update user');
  console.log('  DELETE /api/v2/users/:id - Delete user');
}

// ===== 自动注册支持 =====

// 导出构建器实例，供自动路由系统使用
export const builder = new HonoApiRouteBuilder()
  .resource('users', users)
  .prefix('/api/v3') // 使用 v3 避免与手动注册的 v2 冲突
  .schema(userSchema)
  .filterable({
    name: {
      schema: z.string(),
      operators: ['eq', 'like']
    },
    email: {
      schema: z.string(),
      operators: ['eq']
    },
    createdAt: {
      schema: z.number(),
      operators: ['gt', 'lt', 'between']
    },
  })
  .sortable(['name', 'email', 'createdAt'])
  .searchable(['name', 'email'])
  .pagination({
    defaultLimit: 20,
    maxLimit: 100
  });

// 自定义注册函数，用于演示自动注册的灵活性
export function register(app: Hono) {
  // 1. 注册传统的用户路由到 /api/users
  app.route('/api/users', userRoutes);

  // 2. 注册构建器路由到 /api/v2/users
  userRoutesWithBuilder.register(app);

  // 3. 注册自动构建器路由到 /api/v3/users
  builder.register(app);

}
