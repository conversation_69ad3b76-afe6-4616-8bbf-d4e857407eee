// 使用条件类型定义避免硬依赖
import type { NextRequest } from "../types/runtime";
import type {
  ApiContext,
  HttpMethod,
  MiddlewareContext,
  NextFunction,
} from "../types";
import { QueryUtils, ResponseUtils, ValidationUtils } from "../utils";
import { BaseApiRouteBuilder } from "./base-route-builder";

/**
 * Next.js API Routes 的路由构建器
 * 继承基类，只实现 Next.js 特定的请求处理逻辑
 */
export class ApiRouteBuilder extends BaseApiRouteBuilder {

  private registerCoreRequestMiddleware(
    method: HttpMethod,
    apiContext: ApiContext
  ) {
    // 注册核心请求中间件 数据库交互
    const coreMiddleware = async (
      ctx: MiddlewareContext,
      next: NextFunction
    ) => {
      // 执行查询并设置响应
      const result = await this.executeQuery(method, apiContext)
        .then((res) => ResponseUtils.success(res))
        .catch((err) => err);
      ctx.response = result;
      await next();
    };

    // Use a fixed ID for core middleware
    this.middlewareManager.use(coreMiddleware, "core-request-middleware");
  }

  private async handleRequest(
    req: NextRequest,
    { params }: { params?: Record<string, string> } = {}
  ): Promise<Response> {
    try {
      const url = new URL(req.url);
      const method = req.method as HttpMethod;

      // 1. 验证路由参数
      const validatedParams = ValidationUtils.validateRouteParams(
        params || {},
        this.paramsFields
      );
      if (validatedParams instanceof Response) {
        return validatedParams;
      }

      // 2. 验证请求体
      const bodyValidation = await ValidationUtils.validateRequestBody(
        method,
        req,
        this.schemas
      );
      if (bodyValidation instanceof Response) {
        return bodyValidation;
      }
      const { validatedBody, originalBody } = bodyValidation;
      // 3. 解析查询参数
      const parsedQuery = QueryUtils.parseAllQueryParams(
        url.searchParams,
        this.filterableFields,
        Object.keys(this.paramsFields)
      );

      // 4. 验证查询参数
      const queryValidation = ValidationUtils.validateQueryParams(
        {
          search: parsedQuery.search,
          filter: parsedQuery.filter || {},
          sortFields: parsedQuery.sortFields,
          pagination: parsedQuery.pagination,
        },
        {
          filterableFields: this.filterableFields,
          sortableFields: this.sortableFields,
          pagination: this.paginationConfig,
        }
      );
      if (queryValidation instanceof Response) {
        return queryValidation;
      }

      // 5. 构建 API 上下文
      const apiContext: ApiContext = {
        method,
        params: validatedParams,
        query: {
          search: parsedQuery.search,
          filter: parsedQuery.filter || {},
          sort:
            parsedQuery.sortFields.length > 0
              ? parsedQuery.sortFields
                .map((s) => `${s.field}:${s.order}`)
                .join(",")
              : undefined,
          page: parsedQuery.pagination.page || 1,
          limit: Math.min(
            parsedQuery.pagination.limit || this.paginationConfig.defaultLimit,
            this.paginationConfig.maxLimit
          ),
        },
        originalQuery: url.searchParams,
        originalBody: originalBody,
        body:
          method === "POST"
            ? { ...validatedParams, ...validatedBody }
            : validatedBody,
      };

      // 检查是否为批量操作请求
      const isBatchOperation = apiContext.body && typeof apiContext.body === 'object' && Array.isArray(apiContext.body.batch);

      // 如果是批量操作，使用专门的批量处理方法
      if (isBatchOperation && (["POST", 'PUT', 'DELETE'] as HttpMethod[]).includes(method)) {
        const result = await this.executeBatchOperation(method, apiContext);
        const transformedResult = await this.transformFunction(method, result, this.queryBuilder.db, apiContext).catch((err: Error | Response) => {
          if (err instanceof Response) throw err;
          throw ResponseUtils.internalError(err.message);
        });
        return ResponseUtils.success(transformedResult);
      }

      // 创建中间件上下文
      const middlewareContext: Omit<MiddlewareContext, "middlewareManager"> = {
        req,
        params: apiContext.params,
        searchParams: url.searchParams,
        state: new Map(),
        response: undefined,
      };

      // 设置资源名称到 state
      middlewareContext.state.set("resourceName", this.resourceName);

      // 注册核心请求中间件
      this.registerCoreRequestMiddleware(method, apiContext);

      // 执行中间件链
      await this.middlewareManager.execute(middlewareContext);

      // 返回中间件链的响应或错误
      return (
        middlewareContext.response ||
        ResponseUtils.internalError("No response generated")
      );
    } catch (error) {
      if (error instanceof Response) return error;
      return ResponseUtils.internalError(
        error instanceof Error ? error.message : "Internal server error"
      );
    }
  }



  build() {
    return {
      GET: this.handleRequest.bind(this),
      POST: this.handleRequest.bind(this),
      PUT: this.handleRequest.bind(this),
      DELETE: this.handleRequest.bind(this),
      OPTIONS: () => ResponseUtils.success(null),
    };
  }
}
