# 依赖处理指南

本指南说明如何处理 API 构建器中的框架特定依赖，特别是 `next/server` 等可选依赖。

## 问题背景

在支持多框架的 API 构建器中，我们面临以下挑战：
- Next.js 项目需要 `next/server` 类型
- Hono 项目不需要 Next.js 依赖
- 避免硬依赖导致的安装和编译错误

## 解决方案

### 1. 条件类型定义 (推荐)

**文件**: `src/server/types/runtime.ts`

```typescript
// 定义条件类型，避免硬依赖
export type NextRequest = {
  method: string;
  url: string;
  headers: Headers;
  json(): Promise<any>;
} | any; // 回退到 any
```

**使用方式**:
```typescript
// 替代直接导入
// import type { NextRequest } from "next/server"; ❌
import type { NextRequest } from "../types/runtime"; // ✅
```

### 2. 通用响应工具

**文件**: `src/server/utils/response.ts`

```typescript
// 使用标准 Response API，避免框架特定的响应类
function createJsonResponse(data: any, options = {}): Response {
  return new Response(JSON.stringify(data), {
    status: options.status || 200,
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    }
  });
}
```

### 3. 动态导入和运行时检测

**文件**: `src/server/utils/runtime-detection.ts`

```typescript
// 安全的动态导入
export async function importNextJS() {
  try {
    return await import('next/server');
  } catch {
    return { available: false };
  }
}
```

### 4. 模块声明 (备选)

**文件**: `src/server/types/module-declarations.d.ts`

```typescript
// 为可选依赖提供类型声明
declare module 'next/server' {
  export interface NextRequest {
    method: string;
    url: string;
    // ...
  }
}
```

## 最佳实践

### ✅ 推荐做法

1. **使用条件类型定义**
   ```typescript
   import type { NextRequest } from "../types/runtime";
   ```

2. **使用标准 Web API**
   ```typescript
   // 使用标准 Response 而不是 NextResponse
   return new Response(JSON.stringify(data), { status: 200 });
   ```

3. **运行时检测**
   ```typescript
   if (Environment.isNextJS()) {
     // Next.js 特定逻辑
   }
   ```

### ❌ 避免做法

1. **直接导入框架特定模块**
   ```typescript
   import { NextRequest } from "next/server"; // ❌
   ```

2. **硬编码框架假设**
   ```typescript
   // 假设总是在 Next.js 环境中 ❌
   const response = NextResponse.json(data);
   ```

## 文件结构

```
src/server/
├── types/
│   ├── runtime.ts              # 条件类型定义
│   └── module-declarations.d.ts # 模块声明
├── utils/
│   ├── runtime-detection.ts    # 运行时检测
│   └── response.ts             # 通用响应工具
├── config/
│   └── dependencies.ts         # 依赖管理
└── builders/
    ├── next.ts                 # Next.js 入口
    └── hono.ts                 # Hono 入口
```

## 迁移步骤

如果你有现有的代码需要迁移：

1. **替换直接导入**
   ```typescript
   // 之前
   import { NextRequest } from "next/server";
   
   // 之后
   import type { NextRequest } from "../types/runtime";
   ```

2. **更新响应创建**
   ```typescript
   // 之前
   return NextResponse.json(data);
   
   // 之后
   return createJsonResponse(data);
   ```

3. **添加运行时检测**
   ```typescript
   // 之前
   // 直接使用框架特定功能
   
   // 之后
   if (await DependencyChecker.checkNextJS()) {
     // 使用 Next.js 功能
   }
   ```

## 测试

确保你的代码在不同环境中都能工作：

```bash
# 在 Next.js 项目中测试
npm install next
npm run typecheck

# 在 Hono 项目中测试
npm install hono
npm run typecheck

# 在没有可选依赖的环境中测试
npm uninstall next
npm run typecheck
```

这种方法确保了代码的可移植性和类型安全性，同时避免了不必要的依赖。
