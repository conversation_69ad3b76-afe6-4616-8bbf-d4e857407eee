import { z } from "zod";
import type { SQLiteTableWithColumns } from "drizzle-orm/sqlite-core";
import { MiddlewareManager } from "./core/middleware";
import type { SQL } from "drizzle-orm/sql";
import * as schema from "@/server/db/schema";

// 数据库类型 (支持 libSQL 和 Drizzle)
export type DatabaseInstance = any;


export type HttpMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

// Transaction callback type
export type TransactionCallback<T> = (tx: any, context?: ApiContext) => Promise<T>;

// 关系配置类型
export interface RelationConfig {
  type: "one" | "many";
  table: SQLiteTableWithColumns<any>;
  foreignKey: string;
  fields?: string[];
  through?: {
    table: SQLiteTableWithColumns<any>;
    foreignKey: string;
    targetKey: string;
  };
}

// Relations type for schema
export interface TableRelations {
  [key: string]: RelationConfig;
}

// Drizzle ORM style relation config
export type RelationsConfig = {
  [key: string]:
  | boolean
  | {
    limit?: number;
    offset?: number;
    orderBy?: (
      table: any,
      operators: { asc: Function; desc: Function }
    ) => any[];
    where?: (
      table: any,
      operators: { eq: Function; and: Function; or: Function }
    ) => any;
    extras?: Record<string, SQL.Aliased<unknown>>;
    columns?: Record<string, boolean>;
    with?: RelationsConfig;
  };
};

// 查询配置类型
export interface QueryConfig {
  select?: string[];
  where?: Record<string, any>;
  customWhere?: SQL<unknown> | null;
  orderBy?: string;
  order?: "asc" | "desc";
  limit?: number;
  offset?: number;
  with?: RelationsConfig;
  search?: string;
  filter?: Record<string, any>;
  extras?: string[];
  detail?: boolean;
}

// API 配置类型
export interface ApiConfig {
  path: string;
  method: HttpMethod;
  table: SQLiteTableWithColumns<any>;
  middlewares?: ApiMiddleware[];
  schema?: z.ZodSchema;
  query?: QueryConfig;
}

// 分页配置类型
export interface PaginationConfig {
  defaultLimit: number;
  maxLimit: number;
  defaultPage: number;
}

// Schema 配置类型
export interface SchemaConfig {
  /** @deprecated 创建时使用的schema */
  create?: z.ZodSchema;
  /** @deprecated 更新时使用的schema */
  update?: z.ZodSchema;
  /** @description POST请求时使用的schema */
  POST?: z.ZodSchema;
  /** @description PUT请求时使用的schema */
  PUT?: z.ZodSchema;
}

// 中间件类型 - 使用 any 来支持不同框架的请求对象
export interface MiddlewareContext {
  req: any; // NextRequest 或 Hono Context
  params?: Record<string, string>;
  searchParams?: URLSearchParams;
  state: Map<string, any>;
  response?: Response;
  error?: Error;
  middlewareManager: MiddlewareManager;
}

export type NextFunction = () => Promise<void>;

export type Middleware = (
  context: MiddlewareContext,
  next: NextFunction
) => Promise<void>;

// API 上下文类型
export interface ApiContext {
  method: HttpMethod;
  params: Record<string, string>;
  query: Record<string, any>;
  body: any;
  originalBody: any;
  originalQuery: URLSearchParams;
}

// API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  data: T;
  success: boolean;
  message?: string;
  details?: any;
}

// 分页数据类型
export interface PaginatedData<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// 错误响应类型
export interface ApiErrorResponse {
  code: number;
  message: string;
  details?: any;
}

// 定义支持的操作符类型
export const FILTER_OPERATORS = [
  "eq", // 等于
  "neq", // 不等于
  "gt", // 大于
  "gte", // 大于等于
  "lt", // 小于
  "lte", // 小于等于
  "like", // 模糊匹配
  "in", // 包含
  "between", // 范围
] as const;

export type FilterOperator = (typeof FILTER_OPERATORS)[number];

// 字段过滤配置
export type FilterableField = {
  schema: z.ZodType<any>;
  operators?: FilterOperator[];
};

export interface QueryParams {
  search?: string;
  filter?: Record<string, any>;
  sort?: string;
  order?: "asc" | "desc";
  page?: number;
  limit?: number;
}

// 验证错误类型
export interface ValidationError {
  code: string;
  message: string;
  path: string[];
  [key: string]: any;
}

// API 错误数据类型
export interface ApiErrorData {
  details?: ValidationError[];
  message?: string;
  [key: string]: any;
}

export type ResourceAction = "create" | "read" | "update" | "delete" | "manage";

export interface ResourcePermission {
  resource: string;
  actions: ResourceAction[];
}

export type AuthValidatorFn = (context: {
  user: any;
  method: HttpMethod;
  context: MiddlewareContext;
}) => boolean | Promise<boolean>;

export type MethodAuthConfig = string[] | AuthValidatorFn;

export interface AuthMiddlewareOptions {
  // 全局角色配置
  roles?: string[];
  // HTTP方法权限配置
  methods?: {
    [key in HttpMethod]?: MethodAuthConfig;
  };
}

export interface LoggerMiddlewareOptions {
  level?: "debug" | "info" | "warn" | "error";
  format?: "json" | "text";
}

export interface MetricsMiddlewareOptions {
  prefix?: string;
  labels?: Record<string, string>;
}

export type ApiMiddleware = Middleware;


// 数据转换器类型
export type DataTransformer<T = any> = (method: HttpMethod, data: T, db: DatabaseInstance, apiContext: ApiContext) => T | Promise<T>;