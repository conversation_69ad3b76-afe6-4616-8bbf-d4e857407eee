import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import path from 'path';

// Import database initialization
import { initializeDatabase } from './config/database';

// Import auto-registration utilities
import { autoRegisterRoutes } from './server/core/auto-router';

// Create Hono app
const app = new Hono();

// Global middleware
app.use('*', logger());
app.use('*', cors());
app.use('*', prettyJSON());

// 简化的自动注册方案
async function setupAutoRoutes() {
  console.log('🔄 设置自动路由注册...');

  // 1. 自动发现并注册文件路由
  const router = await autoRegisterRoutes(
    app,
    path.join(process.cwd(), 'src/routes'),
    [
      '**/*.test.*',
      '**/*.spec.*',
      '**/index.*'
    ]
  );

  console.log('📊 已注册路由数量:', router.getRegisteredRoutes().length);
}

// Root route with API documentation
app.get('/', async (c) => {
  return c.json({
    message: 'Welcome to Auto-Registered Hono API',
    version: '2.0.0',
    features: [
      'File-system based auto-registration',
      'Windows path compatibility',
      'Convention over configuration'
    ],
    endpoints: {
      health: '/health',
      users: '/users',
      'auto-users': '/auto-users'
    },
    documentation: {
      'auto-discovery': 'Routes are automatically discovered from src/routes/',
      'conventions': {
        'file-naming': 'kebab-case files become route paths',
        'exports': 'Export "routes" (Hono instance), "builder" (HonoApiRouteBuilder), or "register" (function)',
        'parameters': 'Use [param] in filename for dynamic routes'
      }
    }
  });
});

// Global error handler
app.onError((err, c) => {
  console.error('Global error:', err);
  return c.json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  }, 500);
});

// 404 handler
app.notFound((c) => {
  return c.json({
    error: 'Not Found',
    message: 'The requested endpoint does not exist',
    availableEndpoints: [
      '/health',
      '/api/users',
      '/api/v2/users'
    ]
  }, 404);
});

// Initialize and start server
const port = process.env.PORT || 3000;

async function startServer() {
  try {
    // Initialize database
    await initializeDatabase();
    console.log('✅ 数据库初始化完成');

    // 设置自动路由注册
    await setupAutoRoutes();

    console.log(`🚀 服务器启动在端口 ${port}`);
    console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`);

  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// Start the server
startServer();

export default {
  port,
  fetch: app.fetch,
};
